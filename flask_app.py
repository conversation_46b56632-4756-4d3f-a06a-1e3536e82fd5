#!/usr/bin/env python3
"""
Flask API 服务
提供 HTTP 接口调用 single_sync 和 single_delete 方法
"""

import uuid
import time
import httpx
import json
from flask import Flask, request, jsonify

# 导入配置和日志
from config import config
from logger_config import api_logger as logger

# 创建 Flask 应用
app = Flask(__name__)

# 工具函数
def get_interface_detail_by_id(interface_id):
    """根据接口id查询接口详细信息"""
    try:
        url = config.YAPI_BASE_URL + 'get?id=' + interface_id + '&token=' + config.YAPI_TOKEN
        logger.debug(f'请求接口详情: {url}')

        response = httpx.get(url, timeout=config.REQUEST_TIMEOUT)
        if response.status_code != 200:
            logger.error(f'获取接口详情失败，状态码: {response.status_code}')
            return None

        return response.json()

    except Exception as e:
        logger.error(f'获取接口 {interface_id} 详情时发生错误: {str(e)}')
        return None

def text2vec(data):
    """文本转向量"""
    try:
        embed_data = {"input": data}
        logger.debug(f'开始向量化文本，长度: {len(str(data))}')
        logger.info(f'开始向量化文本，长度: {len(str(data))}')
        embed_response = httpx.post(config.EMBED_URL, json=embed_data, headers=config.EMBED_HEADERS, timeout=config.REQUEST_TIMEOUT)
        if embed_response.status_code != 200:
            logger.error(f'向量化失败，状态码: {embed_response.status_code}')
            return None

        embed_result = embed_response.json()
        vector = embed_result['data'][0]['embedding']
        logger.debug(f'向量化成功，向量维度: {len(vector)}')
        logger.info(f'向量化成功，向量维度: {len(vector)}')

        return vector

    except Exception as e:
        logger.error(f'文本向量化时发生错误: {str(e)}')
        return None

def text2uuid(text):
    """根据文本生成uuid，相同的文本生成的uuid相同"""
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(text)))

def single_sync(interface_id):
    """同步单个接口信息（不会同步删除接口的动作）"""
    try:
        print('------------------------------------------------------------')
        print('开始同步接口: ' + str(interface_id))
        interface_detail = get_interface_detail_by_id(str(interface_id)).get('data')

        if not interface_detail:
            print("接口不存在或获取失败, interface_id:" + str(interface_id))
            return {"success": False, "message": "接口不存在或获取失败", "interface_id": interface_id}

        if interface_detail.get('catid') != config.YAPI_CATID:
            logger.info(f'接口 {interface_id} 不在指定分类下，跳过同步')
            return True

        interface_description = str(interface_detail.get('title')) + '\n' + str(interface_detail.get('markdown'))
        interface_uri = interface_detail.get('query_path').get('path')
        interface_method = interface_detail.get('method')

        # 处理请求体信息
        req_body_other = interface_detail.get('req_body_other', '{}')
        if req_body_other:
            try:
                interface_req_body = json.loads(req_body_other).get('properties', {})
                interface_req_body_required = interface_req_body.get('required', [])
            except (json.JSONDecodeError, AttributeError):
                interface_req_body = {}
                interface_req_body_required = []
        else:
            interface_req_body = {}
            interface_req_body_required = []

        # 标题描述进行向量化
        vector = text2vec(interface_description)
        content = '###' + str(interface_description) + '\n调用方式：' + str(interface_method) + '\nuri:' + str(
            interface_uri) + '\n参数说明' + str(interface_req_body) + '\n不可空的参数列表：' + str(
            interface_req_body_required)

        # insert或update向量数据库
        # 先尝试insert
        insert_url = config.VDB_BASE_URL + 'objects'
        _data = {
            "class": config.VDB_CLASS_NAME,
            "properties": {
                "apiDescription": interface_description,
                "apiDetails": content
            },
            # uuid根据interface_id生成，相同的interface_id生成的uuid相同
            "id": text2uuid(interface_id),
            "creationTimeUnix": int(time.time() * 1000),
            "lastUpdateTimeUnix": int(time.time() * 1000),
            "vector": vector
        }

        insert_response = httpx.post(insert_url, json=_data, headers=config.VDB_HEADERS)
        insert_status = insert_response.status_code
        print('insert结果:' + str(insert_status))

        # insert失败，尝试update
        if insert_status != 200:
            print('已存在对象，尝试update')
            update_url = config.VDB_BASE_URL + 'objects/' + config.VDB_CLASS_NAME + '/' + text2uuid(interface_id)
            update_response = httpx.patch(update_url, json=_data, headers=config.VDB_HEADERS)
            update_status = update_response.status_code
            print('update结果:' + str(update_status))

            if update_status == 204:
                print('接口同步成功（更新）')
                return {"success": True, "message": "接口同步成功（更新）", "interface_id": interface_id, "operation": "update"}
            else:
                print(f'接口同步失败，update状态码: {update_status}')
                return {"success": False, "message": f"接口同步失败，update状态码: {update_status}", "interface_id": interface_id}
        else:
            print('接口同步成功（新增）')
            return {"success": True, "message": "接口同步成功（新增）", "interface_id": interface_id, "operation": "insert"}

    except Exception as e:
        print(f'同步接口 {interface_id} 时发生错误: {str(e)}')
        return {"success": False, "message": f"同步过程中发生错误: {str(e)}", "interface_id": interface_id}

def single_delete(interface_id):
    """删除单个接口信息"""
    try:
        print('------------------------------------------------------------')
        print('开始删除接口: ' + str(interface_id))
        uuid_str = text2uuid(interface_id)
        delete_url = config.VDB_BASE_URL + 'objects/' + config.VDB_CLASS_NAME + '/' + uuid_str
        delete_response = httpx.delete(delete_url, headers=config.VDB_HEADERS)
        delete_status = delete_response.status_code
        print('delete结果:' + str(delete_status))

        if delete_status == 204:
            print("接口删除成功")
            return {"success": True, "message": "接口删除成功", "interface_id": interface_id}
        else:
            print("接口删除失败，状态码:" + str(delete_status))
            return {"success": False, "message": f"接口删除失败，状态码: {delete_status}", "interface_id": interface_id}

    except Exception as e:
        print(f'删除接口 {interface_id} 时发生错误: {str(e)}')
        return {"success": False, "message": f"删除过程中发生错误: {str(e)}", "interface_id": interface_id}

# Flask 路由定义

@app.route('/api/sync/interface', methods=['POST'])
def sync_interface_api():
    """同步单个接口的HTTP接口"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'interface_id' not in data:
            return jsonify({
                "success": False,
                "message": "请求参数错误，需要提供 interface_id"
            }), 400

        interface_id = data['interface_id']

        # 验证 interface_id 是否为有效数字
        try:
            interface_id = int(interface_id)
        except (ValueError, TypeError):
            return jsonify({
                "success": False,
                "message": "interface_id 必须是有效的数字"
            }), 400

        # 调用同步函数
        result = single_sync(interface_id)

        # 根据结果返回相应的HTTP状态码
        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"服务器内部错误: {str(e)}"
        }), 500

@app.route('/api/delete/interface', methods=['POST'])
def delete_interface_api():
    """删除单个接口的HTTP接口"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'interface_id' not in data:
            return jsonify({
                "success": False,
                "message": "请求参数错误，需要提供 interface_id"
            }), 400

        interface_id = data['interface_id']

        # 验证 interface_id 是否为有效数字
        try:
            interface_id = int(interface_id)
        except (ValueError, TypeError):
            return jsonify({
                "success": False,
                "message": "interface_id 必须是有效的数字"
            }), 400

        # 调用删除函数
        result = single_delete(interface_id)

        # 根据结果返回相应的HTTP状态码
        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"服务器内部错误: {str(e)}"
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "message": "VDB Server API is running",
        "timestamp": int(time.time())
    }), 200

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "message": "接口不存在"
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        "success": False,
        "message": "请求方法不被允许"
    }), 405

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "message": "服务器内部错误"
    }), 500

if __name__ == '__main__':
    # 启动 Flask 应用
    app.run(host='0.0.0.0', port=5000, debug=True)
