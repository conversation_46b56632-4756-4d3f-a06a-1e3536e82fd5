#!/usr/bin/env python3
"""
Flask 服务启动脚本
提供更灵活的启动选项
"""

import argparse
import sys
from flask_app import app

def main():
    parser = argparse.ArgumentParser(description='启动 VDB Server Flask API')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--production', action='store_true', help='生产模式（使用 Gunicorn）')
    parser.add_argument('--workers', type=int, default=4, help='Gunicorn worker 数量 (默认: 4)')
    
    args = parser.parse_args()
    
    if args.production:
        # 生产模式使用 Gunicorn
        try:
            import gunicorn.app.wsgiapp as wsgi
            sys.argv = [
                'gunicorn',
                '--bind', f'{args.host}:{args.port}',
                '--workers', str(args.workers),
                '--worker-class', 'sync',
                '--timeout', '120',
                '--max-requests', '1000',
                '--max-requests-jitter', '100',
                'flask_app:app'
            ]
            wsgi.run()
        except ImportError:
            print("错误: 生产模式需要安装 gunicorn")
            print("请运行: pip install gunicorn")
            sys.exit(1)
    else:
        # 开发模式使用 Flask 内置服务器
        print(f"启动 Flask 开发服务器...")
        print(f"地址: http://{args.host}:{args.port}")
        print(f"调试模式: {'开启' if args.debug else '关闭'}")
        print("按 Ctrl+C 停止服务器")
        
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )

if __name__ == '__main__':
    main()
