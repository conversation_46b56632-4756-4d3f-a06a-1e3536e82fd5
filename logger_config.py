#!/usr/bin/env python3
"""
日志配置模块
统一管理应用的日志输出
"""

import logging
import logging.handlers
import os
from datetime import datetime
from config import config

def setup_logger(name: str = 'vdb_server', log_file: str = None) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则使用配置中的默认值

    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)

    # 避免重复添加处理器 - 更严格的检查
    if logger.handlers and logger.hasHandlers():
        return logger

    # 清除现有处理器（防止重复）
    logger.handlers.clear()

    # 设置日志级别
    log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    logger.setLevel(log_level)

    # 防止日志传播到父logger（避免重复输出）
    logger.propagate = False

    # 创建格式化器
    formatter = logging.Formatter(config.LOG_FORMAT)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    if log_file is None:
        log_file = config.LOG_FILE

    # 确保日志目录存在
    log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 使用带日期的日志文件名
    if not os.path.dirname(log_file):
        log_file = os.path.join(log_dir, log_file)

    # 添加日期到文件名
    base_name, ext = os.path.splitext(log_file)
    dated_log_file = f"{base_name}_{datetime.now().strftime('%Y%m%d')}{ext}"

    # 创建轮转文件处理器（每天轮转，保留7天）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        dated_log_file,
        when='midnight',
        interval=1,
        backupCount=7,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

def get_logger(name: str = 'vdb_server') -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        日志记录器
    """
    return logging.getLogger(name)

# 使用单例模式创建日志记录器，避免重复创建
_loggers = {}

def get_singleton_logger(name: str = 'vdb_server', log_file: str = None) -> logging.Logger:
    """
    获取单例日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径

    Returns:
        日志记录器实例
    """
    if name not in _loggers:
        _loggers[name] = setup_logger(name, log_file)
    return _loggers[name]

# 创建默认日志记录器
default_logger = get_singleton_logger()

# 为不同模块创建专用日志记录器
api_logger = get_singleton_logger('vdb_server.api')
sync_logger = get_singleton_logger('vdb_server.sync')
db_logger = get_singleton_logger('vdb_server.db')

if __name__ == '__main__':
    # 测试日志功能
    test_logger = setup_logger('test')
    
    test_logger.debug("这是一条调试信息")
    test_logger.info("这是一条信息")
    test_logger.warning("这是一条警告")
    test_logger.error("这是一条错误")
    test_logger.critical("这是一条严重错误")
    
    print("日志测试完成，请检查日志文件")
