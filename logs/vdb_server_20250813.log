2025-08-13 14:46:01 | INFO     | __main__:test_unified_logger:24 | 这是 loguru logger 的测试消息
2025-08-13 14:46:01 | WARNING  | __main__:test_unified_logger:25 | 这是 loguru logger 的警告消息
2025-08-13 14:46:01 | ERROR    | __main__:test_unified_logger:26 | 这是 loguru logger 的错误消息
2025-08-13 14:46:02 | INFO     | __main__:test_log_file_creation:132 | 测试日志文件写入
2025-08-13 14:59:25,380 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:00:06,606 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:02:51,996 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:03:52,613 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:04:48,018 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:06:45,078 - vdb_server.api - INFO - 开始向量化文本，长度: 85
2025-08-13 15:06:50,895 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:07:44,266 - vdb_server.api - INFO - VDB Server 启动
2025-08-13 15:07:44,266 - vdb_server.api - INFO - 配置验证通过
2025-08-13 15:07:50,885 - vdb_server.api - INFO - 用户确认同步接口 1103
2025-08-13 15:07:50,885 - vdb_server.api - INFO - 开始同步接口: 1103
2025-08-13 15:07:51,772 - vdb_server.api - INFO - 接口信息 - 标题: 商用密码证书数据列表, 方法: POST, URI: /buss/data/queryComPwdAuthList
2025-08-13 15:07:57,531 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:07:57,531 - vdb_server.api - ERROR - 接口 1103 向量化失败
2025-08-13 15:09:18,890 - vdb_server.api - INFO - VDB Server 启动
2025-08-13 15:09:18,890 - vdb_server.api - INFO - 配置验证通过
2025-08-13 15:09:23,505 - vdb_server.api - INFO - 用户确认同步接口 1103
2025-08-13 15:09:23,506 - vdb_server.api - INFO - 开始同步接口: 1103
2025-08-13 15:09:24,433 - vdb_server.api - INFO - 接口信息 - 标题: 商用密码证书数据列表, 方法: POST, URI: /buss/data/queryComPwdAuthList
2025-08-13 15:09:24,433 - vdb_server.api - INFO - https://ai.secsign.online:3003/v1/embeddings
2025-08-13 15:09:24,434 - vdb_server.api - INFO - {'input': '商用密码证书数据列表\n根据产品名称、委托人名称、生产企业名称、生产者(制造商)名称、产品型号、产品版本号、安全等级、有效期、证书状态、产品标准和技术要求，查询商用密码证书'}
2025-08-13 15:09:24,434 - vdb_server.api - INFO - {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG'}
2025-08-13 15:09:24,434 - vdb_server.api - INFO - 30
2025-08-13 15:09:30,254 - vdb_server.api - ERROR - 文本向量化时发生错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:09:30,254 - vdb_server.api - ERROR - 接口 1103 向量化失败
2025-08-13 15:11:28,587 - vdb_server.api - INFO - VDB Server 启动
2025-08-13 15:11:28,587 - vdb_server.api - INFO - 配置验证通过
2025-08-13 15:11:33,961 - vdb_server.api - INFO - 用户确认同步接口 1103
2025-08-13 15:11:33,962 - vdb_server.api - INFO - 开始同步接口: 1103
2025-08-13 15:11:34,345 - vdb_server.api - INFO - 接口信息 - 标题: 商用密码证书数据列表, 方法: POST, URI: /buss/data/queryComPwdAuthList
2025-08-13 15:11:34,345 - vdb_server.api - INFO - https://ai.secsign.online:3003/v1/embeddings
2025-08-13 15:11:34,345 - vdb_server.api - INFO - {'input': '商用密码证书数据列表\n根据产品名称、委托人名称、生产企业名称、生产者(制造商)名称、产品型号、产品版本号、安全等级、有效期、证书状态、产品标准和技术要求，查询商用密码证书'}
2025-08-13 15:11:34,346 - vdb_server.api - INFO - {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG'}
2025-08-13 15:11:34,346 - vdb_server.api - INFO - 30
2025-08-13 15:11:34,989 - vdb_server.api - INFO - insert结果: 422
2025-08-13 15:11:34,989 - vdb_server.api - INFO - 已存在对象，尝试update
2025-08-13 15:11:35,250 - vdb_server.api - INFO - update结果: 204
2025-08-13 15:11:35,251 - vdb_server.api - INFO - 接口 1103 同步成功（更新）
2025-08-13 15:12:02,741 - vdb_server.api - INFO - 开始向量化文本，长度: 85
2025-08-13 15:12:03,110 - vdb_server.api - INFO - 向量化成功，向量维度: 1024
2025-08-13 15:41:03,431 - vdb_server.api - INFO - VDB Server 启动
2025-08-13 15:41:03,432 - vdb_server.api - INFO - 配置验证通过
2025-08-13 15:41:08,288 - vdb_server.api - INFO - 用户确认同步接口 1205
2025-08-13 15:41:08,288 - vdb_server.api - INFO - 开始同步接口: 1205
2025-08-13 15:41:08,682 - vdb_server.api - INFO - 接口详情: {'query_path': {'path': '/product/backfollowup/add', 'params': []}, 'edit_uid': 0, 'status': 'done', 'type': 'static', 'req_body_is_json_schema': True, 'res_body_is_json_schema': True, 'api_opened': False, 'index': 0, 'tag': [], '_id': 1205, 'method': 'POST', 'catid': 348, 'title': '新增产品返回跟进记录', 'path': '/product/backfollowup/add', 'project_id': 46, 'req_params': [], 'res_body_type': 'json', 'uid': 23, 'add_time': 1754468831, 'up_time': 1755053697, 'req_query': [], 'req_headers': [{'required': '1', '_id': '689bfe81e0ad913e7cfce493', 'name': 'Content-Type', 'value': 'application/json'}], 'req_body_form': [], '__v': 0, 'desc': '', 'markdown': '', 'req_body_other': '{"$schema":"http://json-schema.org/draft-04/schema#","type":"object","properties":{"receiptNum":{"type":"string","description":"单据编号"},"subjectSelectName":{"type":"string","description":"主体名字"},"salesManagerName":{"type":"string","description":"销售负责人名称"},"salesManagerId":{"type":"string","description":"销售负责人ID"},"salesManagerDept":{"type":"string","description":"销售负责人部门"},"backBussType":{"type":"string","description":"返回业务类型"},"contractNum":{"type":"string","description":"合同订单编码"},"documentNum":{"type":"string","description":"特殊供货编码"},"customerName":{"type":"string","description":"客户名称"},"supportServiceNodeEndTime":{"type":"string","description":"维保截止日期"},"reasonContent":{"type":"string","description":"原因说明"},"upgradeContents":{"type":"array","items":{"type":"string"},"description":"升级内容"},"upgradeHardware":{"type":"string","description":"硬件升级"},"upgradeHardwareContent":{"type":"string","description":"硬件升级说明"},"upgradeSoftware":{"type":"string","description":"升级软件"},"productionHandlerName":{"type":"string","description":"生产受理人名字"},"productionHandlerId":{"type":"string","description":"生产受理人id"},"chargeOpinion":{"type":"string","description":"收费意见"},"backRequires":{"type":"array","items":{"type":"string"},"description":"返回要求"},"backRequireOther":{"type":"string","description":"其他要求"},"backPlanDate":{"type":"string","description":"计划返回客户日期"},"teamPersonIds":{"type":"string","description":"共享成员ID"},"teamPersonNames":{"type":"string","description":"共享成员名称"},"deliveryServiceCompany":{"type":"string","description":"物流公司"},"backDeliveryNum":{"type":"string","description":"返回运单号"},"cashOnDelivery":{"type":"string","description":"是否到付"},"priceProtectAmount":{"type":"number","description":"价保金额"},"technicalManager":{"type":"string","description":"技术经理"},"technicalManagerAssistant":{"type":"string","description":"指定助理"},"deviceWorkError":{"type":"string","description":"设备是否有故障"},"followupHandlingRecommen":{"type":"string","description":"后续处理意见"},"afterSalesHandlingTime":{"type":"string","description":"售后处理时间"},"afterSalesInvolved":{"type":"string","description":"售后是否参与"},"afterSalesNotInvolvedReason":{"type":"string","description":"售后未参与原因"},"afterSalesPersonId":{"type":"string","description":"售后处理人员id"},"afterSalesPersonName":{"type":"string","description":"售后处理人员名称"},"afterSalesRemark":{"type":"string","description":"售后备注"},"afterSalesChandaoNum":{"type":"string","description":"禅道编号"},"afterSalesEmailAttachs":{"type":"array","items":{"type":"string"},"description":"禅道邮件截图，附件id"},"afterSalesDeviceWorkError":{"type":"string","description":"设备故障反馈"},"afterSalesDeviceWorkErrorAttachs":{"type":"array","items":{"type":"string"},"description":"故障单上传，附件id"},"productDatas":{"type":"array","items":{"type":"object","properties":{"unineCode":{"type":"string","description":"U9编码"},"productType":{"type":"string","description":"产品分类"},"productCode":{"type":"string","description":"产品编码"},"productBrand":{"type":"string","description":"品牌名称"},"productName":{"type":"string","description":"产品名称"},"productModel":{"type":"string","description":"产品型号"},"productionBatchNumber":{"type":"string","description":"生产批号"},"productionSerialNumber":{"type":"string","description":"序列号"},"productNum":{"type":"number","description":"产品数量"},"sourceOrderNum":{"type":"string","description":"源单单据编号"},"supplyProductType":{"type":"string","description":"供货类型"},"productOrderNum":{"type":"string","description":"U9单据编号"},"backWarehouse":{"type":"string","description":"返回仓库"}},"required":["unineCode","productType","productCode","productName","productModel","productionBatchNumber","productionSerialNumber","productNum","sourceOrderNum","supplyProductType","backWarehouse"]},"description":"产品档案列表"},"productCheckResult":{"type":"string","description":"产品质检结果"},"productCheckQuestion":{"type":"string","description":"质检问题"},"productServiceRepair":{"type":"string","description":"维修"},"processChangeReason":{"type":"string","description":"流程变更原因"},"productCheckReport":{"type":"array","items":{"type":"string"},"description":"质检报告，附件id"},"businessFeedbackAdvice":{"type":"string","description":"业务反馈处理意见"},"checkHandleResult":{"type":"string","description":"质检处理结果"},"productStorageType":{"type":"string","description":"入库类型"}},"required":[]}', 'req_body_type': 'json', 'res_body': '{"$schema":"http://json-schema.org/draft-04/schema#","type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string"},"time":{"type":"number"},"code":{"type":"string"},"object":{"type":"null"}}}', 'username': 'liwei'}
2025-08-13 15:41:08,682 - vdb_server.api - INFO - 接口信息 - 标题: 新增产品返回跟进记录, 方法: POST, URI: /product/backfollowup/add
2025-08-13 15:41:09,282 - vdb_server.api - INFO - insert结果: 422
2025-08-13 15:41:09,282 - vdb_server.api - INFO - 已存在对象，尝试update
2025-08-13 15:41:09,549 - vdb_server.api - INFO - update结果: 204
2025-08-13 15:41:09,549 - vdb_server.api - INFO - 接口 1205 同步成功（更新）
2025-08-13 15:42:40,656 - vdb_server.api - INFO - VDB Server 启动
2025-08-13 15:42:40,656 - vdb_server.api - INFO - 配置验证通过
2025-08-13 15:42:47,866 - vdb_server.api - INFO - 用户确认同步接口 1025
2025-08-13 15:42:47,866 - vdb_server.api - INFO - 开始同步接口: 1025
2025-08-13 15:42:48,224 - vdb_server.api - INFO - 接口 1025 不在指定分类下，跳过同步
