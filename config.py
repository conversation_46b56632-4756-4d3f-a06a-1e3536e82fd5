#!/usr/bin/env python3
"""
配置文件
包含 YApi、向量数据库、嵌入模型等配置信息
"""

import os
from typing import Dict, Any

class Config:
    """配置类"""

    # YApi 基础信息配置
    YAPI_BASE_URL = os.getenv('YAPI_BASE_URL', 'http://10.0.107.253:3000/api/interface/')
    YAPI_TOKEN = os.getenv('YAPI_TOKEN', 'e84e14ddd978c534dd2aa872b4e5ae79e989dcea0d24b47b6da1686b96b81d84')
    YAPI_CATID = os.getenv('YAPI_CATID','303')
    
    # 向量数据库配置
    VDB_BASE_URL = os.getenv('VDB_BASE_URL','http://10.20.35.250:8009/v1/')
    VDB_AUTHORIZATION = os.getenv('VDB_AUTHORIZATION','Bearer QTsBTQWbr6iESeOoVKgiejFDhQogiSON')
    VDB_CLASS_NAME = os.getenv('VDB_CLASS_NAME','Crm_Ai_Interface')
    
    @property
    def VDB_HEADERS(self) -> Dict[str, str]:
        """向量数据库请求头"""
        return {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.VDB_AUTHORIZATION
        }
    
    # 嵌入模型配置
    EMBED_URL = os.getenv('EMBED_URL','https://ai.secsign.online:3003/v1/embeddings')
    EMBED_AUTHORIZATION = os.getenv('EMBED_AUTHORIZATION','Bearer sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG')
    
    @property
    def EMBED_HEADERS(self) -> Dict[str, str]:
        """嵌入模型请求头"""
        return {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.EMBED_AUTHORIZATION
        }
    
    # Flask 应用配置
    FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    FLASK_PORT = int(os.getenv('FLASK_PORT', '5000'))
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    LOG_FILE = os.getenv('LOG_FILE', 'vdb_server.log')
    
    # HTTP 请求配置
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))
    REQUEST_RETRIES = int(os.getenv('REQUEST_RETRIES', '3'))
    
    @classmethod
    def get_all_config(cls) -> Dict[str, Any]:
        """获取所有配置信息（用于调试）"""
        config_dict = {}
        for attr in dir(cls):
            if not attr.startswith('_') and not callable(getattr(cls, attr)):
                value = getattr(cls, attr)
                # 隐藏敏感信息
                if 'TOKEN' in attr or 'AUTHORIZATION' in attr:
                    if isinstance(value, str) and len(value) > 10:
                        value = value[:10] + '...'
                config_dict[attr] = value
        return config_dict
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            'YAPI_BASE_URL', 'YAPI_TOKEN', 'YAPI_CATID',
            'VDB_BASE_URL', 'VDB_AUTHORIZATION', 'VDB_CLASS_NAME',
            'EMBED_URL', 'EMBED_AUTHORIZATION'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            value = getattr(cls, config_name, None)
            if not value or (isinstance(value, str) and not value.strip()):
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"错误: 缺少必要配置: {', '.join(missing_configs)}")
            return False
        
        return True

# 创建全局配置实例
config = Config()

# 配置验证
if __name__ == '__main__':
    print("=== 配置信息 ===")
    for key, value in Config.get_all_config().items():
        print(f"{key}: {value}")
    
    print("\n=== 配置验证 ===")
    if Config.validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
