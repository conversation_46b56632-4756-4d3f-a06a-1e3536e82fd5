# VDB Server - 接口信息知识库构建工具

VDB Server 是一个用于将 YApi 接口信息同步到向量数据库的工具，支持全量同步、增量同步和删除操作。提供了命令行交互式界面和 HTTP API 两种使用方式。

## 🚀 功能特性

- ✅ **多种同步模式**：支持全量重建、全量同步、单个同步、单个删除
- ✅ **交互式界面**：友好的命令行交互界面，支持参数验证和操作确认
- ✅ **HTTP API 服务**：提供 RESTful API 接口，支持远程调用
- ✅ **配置文件管理**：统一的配置文件管理，支持环境变量覆盖
- ✅ **完善的日志系统**：分级日志记录，支持文件轮转和控制台输出
- ✅ **错误处理**：完善的异常处理和错误恢复机制
- ✅ **容器化支持**：提供 Docker 和 Docker Compose 配置
- ✅ **生产就绪**：支持 Gunicorn 生产部署

## 📁 项目结构

```
VDB-Server/
├── manual.py                  # 主程序（交互式界面）
├── flask_app.py           # Flask API 服务
├── config.py              # 配置文件
├── logger_config.py       # 日志配置
├── start_server.py        # 服务启动脚本
├── requirements.txt      # Python 依赖
├── Dockerfile           # Docker 配置
├── docker-compose.yml   # Docker Compose 配置
├── API_README.md        # API 文档
└── README.md           # 项目文档
```

## 🛠️ 安装和配置

### 1. 环境要求

- Python 3.7+
- 网络访问：YApi 服务、向量数据库、嵌入模型服务

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

配置信息在 `config.py` 中定义，支持环境变量覆盖：

#### YApi 配置
```python
YAPI_BASE_URL = 'http://************:3000/api/interface/'
YAPI_TOKEN = 'your_yapi_token'
YAPI_CATID = '303'
```

#### 向量数据库配置
```python
VDB_BASE_URL = 'http://************:8009/v1/'
VDB_AUTHORIZATION = 'Bearer your_vdb_token'
VDB_CLASS_NAME = 'Zhuang_test'
```

#### 嵌入模型配置
```python
EMBED_URL = 'https://ai.secsign.online:3003/v1/embeddings'
EMBED_AUTHORIZATION = 'Bearer your_embed_token'
```

#### 环境变量覆盖示例
```bash
export YAPI_TOKEN="your_new_token"
export VDB_CLASS_NAME="your_class_name"
export LOG_LEVEL="DEBUG"
```

## 🎯 使用方法

### 方式一：交互式命令行界面

```bash
python manual.py
```

启动后会显示操作菜单：

```
==================================================
VDB Server 接口同步工具
==================================================
请选择要执行的操作：
1. 全量重建 (full_rebase) - 删除所有数据后重新同步
2. 全量同步 (full_sync) - 同步分类下所有接口
3. 单个同步 (single_sync) - 同步指定接口
4. 单个删除 (single_delete) - 删除指定接口
5. 查看配置信息
0. 退出程序
==================================================
```

#### 操作说明

1. **全量重建**：删除向量数据库中的所有数据，然后重新同步所有接口
2. **全量同步**：同步 YApi 分类下的所有接口（不删除现有数据）
3. **单个同步**：同步指定 ID 的接口
4. **单个删除**：从向量数据库中删除指定接口
5. **查看配置**：显示当前配置信息（敏感信息会被隐藏）

### 方式二：HTTP API 服务

#### 启动 API 服务

```bash
# 开发模式
python flask_app.py

# 或使用启动脚本
python start_server.py --debug

# 生产模式
python start_server.py --production --workers 4
```

#### API 接口

**1. 同步接口**
```bash
POST /api/sync/interface
Content-Type: application/json

{
    "interface_id": 1103
}
```

**2. 删除接口**
```bash
DELETE /api/delete/interface
Content-Type: application/json

{
    "interface_id": 1103
}
```

**3. 健康检查**
```bash
GET /api/health
```

详细的 API 文档请参考 [API_README.md](API_README.md)

## 📊 日志系统

### 日志级别
- `DEBUG`：调试信息
- `INFO`：一般信息
- `WARNING`：警告信息
- `ERROR`：错误信息
- `CRITICAL`：严重错误

### 日志配置
```python
LOG_LEVEL = 'INFO'                    # 日志级别
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = 'vdb_server.log'          # 日志文件
```

### 日志文件
- 日志文件按日期轮转：`vdb_server_20231201.log`
- 保留最近 7 天的日志文件
- 同时输出到控制台和文件

## 🧪 测试

### 运行测试
```bash
# API 功能测试
python test_api.py

# 使用示例
python example_usage.py

# 批量同步示例
python example_usage.py batch
```

### 配置验证
```bash
python config.py
```

## 🐳 Docker 部署

### 构建镜像
```bash
docker build -t vdb-server .
```

### 运行容器
```bash
docker run -p 5000:5000 vdb-server
```

### 使用 Docker Compose
```bash
docker-compose up -d
```

## 🔧 开发和调试

### 开发环境设置
```bash
# 设置调试模式
export FLASK_DEBUG=true
export LOG_LEVEL=DEBUG

# 启动开发服务器
python flask_app.py
```

### 常见问题排查

1. **配置验证失败**
   - 检查配置文件中的 URL 和 Token 是否正确
   - 确认网络连接正常

2. **接口同步失败**
   - 检查 YApi 服务是否可访问
   - 验证接口 ID 是否存在
   - 查看日志文件获取详细错误信息

3. **向量化失败**
   - 检查嵌入模型服务是否正常
   - 确认 API Token 是否有效

4. **向量数据库操作失败**
   - 检查向量数据库服务状态
   - 验证认证信息是否正确

## 📈 性能优化

### 建议配置
- **生产环境**：使用 Gunicorn + Nginx
- **并发处理**：根据服务器配置调整 worker 数量
- **日志级别**：生产环境建议使用 INFO 或 WARNING
- **超时设置**：根据网络情况调整 `REQUEST_TIMEOUT`

### 监控指标
- 同步成功率
- 响应时间
- 错误日志
- 系统资源使用情况

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 支持

如有问题或建议，请：
1. 查看日志文件获取详细错误信息
2. 检查配置是否正确
3. 提交 Issue 或联系开发团队

---

**注意**：请确保在生产环境中妥善保管 API Token 等敏感信息，建议使用环境变量或密钥管理系统。
